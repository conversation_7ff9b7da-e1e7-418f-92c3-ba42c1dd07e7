import React, { useState } from 'react';
import {
  Box, Typography, Paper, Grid, Button, TextField,
  Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, Chip, IconButton, Card, CardContent,
  Dialog, DialogTitle, DialogContent, DialogActions,
  FormControl, InputLabel, Select, MenuItem, SelectChangeEvent,
  Tabs, Tab, LinearProgress
} from '@mui/material';
import {
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  PictureAsPdf as PdfIcon,
  InsertDriveFile as FileIcon,
  BarChart as ChartIcon,
  Search as SearchIcon
} from '@mui/icons-material';

interface Report {
  id: number;
  name: string;
  type: string;
  generatedDate: string;
  status: string;
  score: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`report-tabpanel-${index}`}
      aria-labelledby={`report-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const Reports: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([
    { id: 1, name: 'Quarterly Compliance Report Q1 2025', type: 'Compliance', generatedDate: '2025-04-01', status: 'Complete', score: 87 },
    { id: 2, name: 'Policy Coverage Analysis', type: 'Policy', generatedDate: '2025-03-15', status: 'Complete', score: 92 },
    { id: 3, name: 'FEMA Requirements Gap Analysis', type: 'Gap Analysis', generatedDate: '2025-03-22', status: 'Complete', score: 76 },
    { id: 4, name: 'Document Completeness Report', type: 'Document', generatedDate: '2025-04-05', status: 'In Progress', score: 45 },
    { id: 5, name: 'Annual Security Assessment', type: 'Security', generatedDate: '2025-02-28', status: 'Complete', score: 81 },
  ]);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('All');
  const [tabValue, setTabValue] = useState(0);
  const [openGenerateDialog, setOpenGenerateDialog] = useState(false);
  const [reportType, setReportType] = useState('Compliance');
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };
  
  const handleFilterChange = (event: SelectChangeEvent) => {
    setFilterType(event.target.value);
  };
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleGenerateDialogOpen = () => {
    setOpenGenerateDialog(true);
  };
  
  const handleGenerateDialogClose = () => {
    setOpenGenerateDialog(false);
  };
  
  const handleTypeChange = (event: SelectChangeEvent) => {
    setReportType(event.target.value);
  };
  
  const handleGenerateReport = () => {
    // Simulate generating a new report
    const newReport: Report = {
      id: reports.length + 1,
      name: `${reportType} Report - ${new Date().toLocaleDateString()}`,
      type: reportType,
      generatedDate: new Date().toISOString().split('T')[0],
      status: 'In Progress',
      score: 0,
    };
    
    setReports([...reports, newReport]);
    handleGenerateDialogClose();
  };
  
  const filteredReports = reports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'All' || report.type === filterType;
    return matchesSearch && matchesFilter;
  });
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Complete': return 'success';
      case 'In Progress': return 'warning';
      case 'Failed': return 'error';
      default: return 'default';
    }
  };
  
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Reports
      </Typography>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="My Reports" />
          <Tab label="Scheduled Reports" />
          <Tab label="Templates" />
        </Tabs>
      </Paper>
      
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 2, mb: 3 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    placeholder="Search reports..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                    InputProps={{
                      startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel>Report Type</InputLabel>
                    <Select
                      value={filterType}
                      label="Report Type"
                      onChange={handleFilterChange}
                    >
                      <MenuItem value="All">All Types</MenuItem>
                      <MenuItem value="Compliance">Compliance</MenuItem>
                      <MenuItem value="Policy">Policy</MenuItem>
                      <MenuItem value="Gap Analysis">Gap Analysis</MenuItem>
                      <MenuItem value="Document">Document</MenuItem>
                      <MenuItem value="Security">Security</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: { xs: 'flex-start', sm: 'flex-end' } }}>
                  <Button
                    variant="contained"
                    startIcon={<ChartIcon />}
                    onClick={handleGenerateDialogOpen}
                  >
                    Generate Report
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
          
          <Grid item xs={12}>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Report Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Generated Date</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Compliance Score</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredReports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {report.type === 'Compliance' ? (
                            <ChartIcon sx={{ mr: 1, color: 'primary.main' }} />
                          ) : (
                            <PdfIcon sx={{ mr: 1, color: 'error.main' }} />
                          )}
                          {report.name}
                        </Box>
                      </TableCell>
                      <TableCell>{report.type}</TableCell>
                      <TableCell>{report.generatedDate}</TableCell>
                      <TableCell>
                        <Chip
                          label={report.status}
                          color={getStatusColor(report.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {report.status === 'In Progress' ? (
                          <LinearProgress variant="indeterminate" sx={{ height: 10, borderRadius: 5 }} />
                        ) : (
                          <>
                            <Typography variant="body2" component="span" sx={{ mr: 1 }}>
                              {report.score}%
                            </Typography>
                            <LinearProgress 
                              variant="determinate" 
                              value={report.score} 
                              sx={{ 
                                height: 10, 
                                borderRadius: 5,
                                backgroundColor: '#e0e0e0',
                                '& .MuiLinearProgress-bar': {
                                  backgroundColor: report.score >= 80 ? '#4caf50' : 
                                                  report.score >= 60 ? '#ff9800' : '#f44336'
                                }
                              }} 
                            />
                          </>
                        )}
                      </TableCell>
                      <TableCell align="right">
                        <IconButton 
                          size="small" 
                          color="primary"
                          disabled={report.status === 'In Progress'}
                        >
                          <DownloadIcon />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          color="primary"
                          disabled={report.status === 'In Progress'}
                        >
                          <FileIcon />
                        </IconButton>
                        <IconButton 
                          size="small" 
                          color="primary"
                          disabled={report.status !== 'In Progress'}
                        >
                          <RefreshIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        </Grid>
      </TabPanel>
      
      <TabPanel value={tabValue} index={1}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Scheduled Reports
            </Typography>
            <Typography variant="body1">
              You have 3 scheduled reports configured.
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                • Weekly Compliance Summary - Every Monday at 8:00 AM
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Monthly Policy Coverage Report - 1st of each month
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Quarterly Gap Analysis - End of each quarter
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </TabPanel>
      
      <TabPanel value={tabValue} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Report Templates
            </Typography>
            <Typography variant="body1">
              Available templates for generating custom reports.
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                • Comprehensive Compliance Report
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Executive Summary Dashboard
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Policy Coverage Analysis
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • FEMA Requirements Gap Analysis
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Document Completeness Report
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </TabPanel>
      
      {/* Generate Report Dialog */}
      <Dialog open={openGenerateDialog} onClose={handleGenerateDialogClose}>
        <DialogTitle>Generate New Report</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1, pb: 2, minWidth: '400px' }}>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Report Type</InputLabel>
              <Select
                value={reportType}
                label="Report Type"
                onChange={handleTypeChange}
              >
                <MenuItem value="Compliance">Compliance Report</MenuItem>
                <MenuItem value="Policy">Policy Coverage Analysis</MenuItem>
                <MenuItem value="Gap Analysis">Gap Analysis</MenuItem>
                <MenuItem value="Document">Document Completeness</MenuItem>
                <MenuItem value="Security">Security Assessment</MenuItem>
              </Select>
            </FormControl>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              This will generate a new report based on your current document library and compliance status.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleGenerateDialogClose}>Cancel</Button>
          <Button 
            onClick={handleGenerateReport} 
            variant="contained"
          >
            Generate
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Reports;
