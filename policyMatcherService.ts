import axios from 'axios';

// Base API URL - should be configured based on environment
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Policy Matcher API service
export const policyMatcherService = {
  // Upload requirements document and start matching process
  uploadRequirements: async (file, options = {}) => {
    const formData = new FormData();
    formData.append('requirements_file', file);
    
    if (options.confidenceThreshold) {
      formData.append('confidence_threshold', options.confidenceThreshold);
    }
    
    if (options.matchingAlgorithm) {
      formData.append('matching_algorithm', options.matchingAlgorithm);
    }
    
    return apiClient.post('/policy-matcher/match', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Get status of a matching job
  getJobStatus: async (jobId) => {
    return apiClient.get(`/policy-matcher/jobs/${jobId}/status`);
  },
  
  // Get results of a completed matching job
  getJobResults: async (jobId) => {
    return apiClient.get(`/policy-matcher/jobs/${jobId}/results`);
  },
  
  // Get HTML report for a completed matching job
  getJobReport: async (jobId) => {
    return apiClient.get(`/policy-matcher/jobs/${jobId}/report`, {
      responseType: 'blob',
    });
  },
  
  // Get all available policies
  getPolicies: async () => {
    return apiClient.get('/policy-matcher/policies');
  },
  
  // Get matching history
  getMatchingHistory: async (page = 1, limit = 10) => {
    return apiClient.get('/policy-matcher/history', {
      params: { page, limit },
    });
  },
  
  // Delete a matching job
  deleteJob: async (jobId) => {
    return apiClient.delete(`/policy-matcher/jobs/${jobId}`);
  },
};

export default policyMatcherService;
