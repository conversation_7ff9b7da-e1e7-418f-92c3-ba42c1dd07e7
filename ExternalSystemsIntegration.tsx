import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Grid, Card, CardContent,
  FormControl, InputLabel, Select, MenuItem, SelectChangeEvent,
  Button, Tabs, Tab, CircularProgress, Divider, TextField,
  Dialog, DialogTitle, DialogContent, DialogActions,
  List, ListItem, ListItemText, ListItemIcon, IconButton,
  Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, Chip, Switch, FormControlLabel
} from '@mui/material';
import {
  CloudDownload as DownloadIcon,
  Event as EventIcon,
  Link as LinkIcon,
  Search as SearchIcon,
  Sync as SyncIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Folder as FolderIcon,
  Description as DocumentIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`integration-tabpanel-${index}`}
      aria-labelledby={`integration-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Mock FEMA data
const femaDisasterData = [
  { id: 'DR-4611', name: 'Hurricane Alpha', state: 'Florida', declarationDate: '2025-03-15', type: 'Hurricane', status: 'Active' },
  { id: 'DR-4612', name: 'Severe Storms and Flooding', state: 'Texas', declarationDate: '2025-02-28', type: 'Flood', status: 'Active' },
  { id: 'DR-4613', name: 'Wildfire', state: 'California', declarationDate: '2025-01-20', type: 'Fire', status: 'Closed' },
  { id: 'DR-4614', name: 'Tornado', state: 'Oklahoma', declarationDate: '2025-04-05', type: 'Tornado', status: 'Active' },
  { id: 'DR-4615', name: 'Winter Storm', state: 'Michigan', declarationDate: '2025-02-10', type: 'Winter Storm', status: 'Closed' },
];

const femaAssistancePrograms = [
  { id: 'PA', name: 'Public Assistance', description: 'Provides assistance to state, local, tribal, and territorial governments for emergency work and repair/replacement of disaster-damaged facilities.' },
  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },
  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },
  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },
  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },
];

// Mock document management system data
const documentRepositories = [
  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },
  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },
  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },
  { id: 4, name: 'OneDrive', connected: true, lastSync: '2025-04-10 02:45 PM', documentCount: 42 },
  { id: 5, name: 'Box', connected: false, lastSync: 'Never', documentCount: 0 },
];

const recentDocuments = [
  { id: 1, name: 'Disaster Recovery Plan.docx', repository: 'SharePoint', lastModified: '2025-04-12', size: '2.4 MB' },
  { id: 2, name: 'FEMA Compliance Checklist.xlsx', repository: 'Google Drive', lastModified: '2025-04-11', size: '1.8 MB' },
  { id: 3, name: 'Emergency Response Procedures.pdf', repository: 'SharePoint', lastModified: '2025-04-10', size: '3.5 MB' },
  { id: 4, name: 'Hazard Mitigation Plan.docx', repository: 'OneDrive', lastModified: '2025-04-09', size: '5.2 MB' },
  { id: 5, name: 'Public Assistance Application.pdf', repository: 'Google Drive', lastModified: '2025-04-08', size: '1.2 MB' },
];

// Mock calendar events
const calendarEvents = [
  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },
  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },
  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },
  { id: 4, title: 'Policy Update Training', start: '2025-05-10', end: '2025-05-10', type: 'training', calendar: 'Team' },
  { id: 5, title: 'Disaster Recovery Exercise', start: '2025-05-15', end: '2025-05-16', type: 'exercise', calendar: 'Emergency' },
];

const calendarSources = [
  { id: 1, name: 'Google Calendar', connected: true, lastSync: '2025-04-13 11:30 AM' },
  { id: 2, name: 'Microsoft Outlook', connected: true, lastSync: '2025-04-13 10:45 AM' },
  { id: 3, name: 'Apple Calendar', connected: false, lastSync: 'Never' },
];

const ExternalSystemsIntegration: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  
  // FEMA integration state
  const [femaSearchQuery, setFemaSearchQuery] = useState('');
  const [selectedDisaster, setSelectedDisaster] = useState<string | null>(null);
  const [femaConnected, setFemaConnected] = useState(true);
  const [femaLastSync, setFemaLastSync] = useState('2025-04-13 08:15 AM');
  
  // Document management state
  const [selectedRepository, setSelectedRepository] = useState<number | null>(null);
  const [documentSearchQuery, setDocumentSearchQuery] = useState('');
  const [repositoryDialogOpen, setRepositoryDialogOpen] = useState(false);
  const [newRepositoryName, setNewRepositoryName] = useState('');
  const [newRepositoryUrl, setNewRepositoryUrl] = useState('');
  
  // Calendar integration state
  const [calendarView, setCalendarView] = useState('month');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [eventDialogOpen, setEventDialogOpen] = useState(false);
  const [newEventTitle, setNewEventTitle] = useState('');
  const [newEventStart, setNewEventStart] = useState('');
  const [newEventEnd, setNewEventEnd] = useState('');
  const [newEventType, setNewEventType] = useState('');
  const [newEventCalendar, setNewEventCalendar] = useState('');
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // FEMA integration handlers
  const handleFemaSearch = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };
  
  const handleFemaSync = () => {
    setLoading(true);
    // Simulate sync
    setTimeout(() => {
      setLoading(false);
      setFemaLastSync(new Date().toLocaleString());
    }, 2000);
  };
  
  const handleSelectDisaster = (disasterId: string) => {
    setSelectedDisaster(disasterId === selectedDisaster ? null : disasterId);
  };
  
  // Document management handlers
  const handleDocumentSearch = () => {
    setLoading(true);
    // Simulate search
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };
  
  const handleRepositorySync = (repositoryId: number) => {
    setLoading(true);
    // Simulate sync
    setTimeout(() => {
      setLoading(false);
      // Update last sync time
      const updatedRepositories = documentRepositories.map(repo => 
        repo.id === repositoryId 
          ? { ...repo, lastSync: new Date().toLocaleString() }
          : repo
      );
    }, 1500);
  };
  
  const handleOpenRepositoryDialog = () => {
    setNewRepositoryName('');
    setNewRepositoryUrl('');
    setRepositoryDialogOpen(true);
  };
  
  const handleCloseRepositoryDialog = () => {
    setRepositoryDialogOpen(false);
  };
  
  const handleAddRepository = () => {
    // Simulate adding repository
    // In a real app, this would call an API
    handleCloseRepositoryDialog();
  };
  
  const handleSelectRepository = (repositoryId: number) => {
    setSelectedRepository(repositoryId === selectedRepository ? null : repositoryId);
  };
  
  // Calendar integration handlers
  const handleOpenEventDialog = () => {
    setNewEventTitle('');
    setNewEventStart('');
    setNewEventEnd('');
    setNewEventType('');
    setNewEventCalendar('');
    setEventDialogOpen(true);
  };
  
  const handleCloseEventDialog = () => {
    setEventDialogOpen(false);
  };
  
  const handleAddEvent = () => {
    // Simulate adding event
    // In a real app, this would call an API
    handleCloseEventDialog();
  };
  
  const handleCalendarViewChange = (event: SelectChangeEvent) => {
    setCalendarView(event.target.value);
  };
  
  const filteredFemaDisasters = femaSearchQuery
    ? femaDisasterData.filter(disaster => 
        disaster.id.toLowerCase().includes(femaSearchQuery.toLowerCase()) ||
        disaster.name.toLowerCase().includes(femaSearchQuery.toLowerCase()) ||
        disaster.state.toLowerCase().includes(femaSearchQuery.toLowerCase())
      )
    : femaDisasterData;
    
  const filteredDocuments = documentSearchQuery
    ? recentDocuments.filter(doc => 
        doc.name.toLowerCase().includes(documentSearchQuery.toLowerCase()) ||
        doc.repository.toLowerCase().includes(documentSearchQuery.toLowerCase())
      )
    : recentDocuments;
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        External Systems Integration
      </Typography>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="FEMA Database" />
          <Tab label="Document Management" />
          <Tab label="Calendar Integration" />
        </Tabs>
      </Paper>
      
      {/* FEMA Database Integration */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  FEMA Disaster Database
                </Typography>
                <Box>
                  <Button
                    variant="outlined"
                    startIcon={<SyncIcon />}
                    onClick={handleFemaSync}
                    sx={{ mr: 1 }}
                  >
                    Sync
                  </Button>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={femaConnected}
                        onChange={() => setFemaConnected(!femaConnected)}
                        color="primary"
                      />
                    }
                    label="Connected"
                  />
                </Box>
              </Box>
              
              <Box sx={{ display: 'flex', mb: 3 }}>
                <TextField
                  fullWidth
                  placeholder="Search disasters by ID, name, or state"
                  variant="outlined"
                  value={femaSearchQuery}
                  onChange={(e) => setFemaSearchQuery(e.target.value)}
                  sx={{ mr: 1 }}
                />
                <Button
                  variant="contained"
                  startIcon={<SearchIcon />}
                  onClick={handleFemaSearch}
                >
                  Search
                </Button>
              </Box>
              
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Disaster ID</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>State</TableCell>
                        <TableCell>Declaration Date</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredFemaDisasters.map((disaster) => (
                        <TableRow 
                          key={disaster.id}
                          selected={selectedDisaster === disaster.id}
                          onClick={() => handleSelectDisaster(disaster.id)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell>{disaster.id}</TableCell>
                          <TableCell>{disaster.name}</TableCell>
                          <TableCell>{disaster.state}</TableCell>
                          <TableCell>{disaster.declarationDate}</TableCell>
                          <TableCell>{disaster.type}</TableCell>
                          <TableCell>
                            <Chip
                              label={disaster.status}
                              color={disaster.status === 'Active' ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
              
              <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  Last synchronized: {femaLastSync}
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  disabled={!selectedDisaster}
                >
                  Import Selected Disaster
                </Button>
              </Box>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  FEMA Integration Status
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: femaConnected ? 'success.main' : 'error.main',
                      mr: 1
                    }}
                  />
                  <Typography>
                    {femaConnected ? 'Connected' : 'Disconnected'}
                  </Typography>
                </Box>
                <Typography variant="body2" paragraph>
                  Last synchronized: {femaLastSync}
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  Available Data:
                </Typography>
                <Typography variant="body2">
                  • Disaster Declarations<br />
                  • Assistance Programs<br />
                  • Eligibility Requirements<br />
                  • Application Deadlines<br />
                  • Funding Categories
                </Typography>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Assistance Programs
                </Typography>
                <List dense>
                  {femaAssistancePrograms.map((program) => (
                    <ListItem key={program.id}>
                      <ListItemText
                        primary={`${program.id}: ${program.name}`}
                        secondary={program.description}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
      
      {/* Document Management Integration */}
      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Connected Document Repositories
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleOpenRepositoryDialog}
                >
                  Add Repository
                </Button>
              </Box>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Repository</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Last Sync</TableCell>
                      <TableCell>Documents</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {documentRepositories.map((repository) => (
                      <TableRow 
                        key={repository.id}
                        selected={selectedRepository === repository.id}
                        onClick={() => handleSelectRepository(repository.id)}
                        sx={{ cursor: 'pointer' }}
                      >
                        <TableCell>{repository.name}</TableCell>
                        <TableCell>
                          <Chip
                            label={repository.connected ? 'Connected' : 'Disconnected'}
                            color={repository.connected ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{repository.lastSync}</TableCell>
                        <TableCell>{repository.documentCount}</TableCell>
                        <TableCell align="right">
                          <IconButton 
                            color="primary"
                            onClick={() => handleRepositorySync(repository.id)}
                            disabled={!repository.connected}
                          >
                            <SyncIcon />
                          </IconButton>
                          <IconButton color="primary">
                            <SettingsIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
            
            <Paper sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Recent Documents
                </Typography>
                <Box sx={{ display: 'flex' }}>
                  <TextField
                    placeholder="Search documents"
                    variant="outlined"
                    size="small"
                    value={documentSearchQuery}
                    onChange={(e) => setDocumentSearchQuery(e.target.value)}
                    sx={{ mr: 1 }}
                  />
                  <Button
                    variant="outlined"
                    startIcon={<SearchIcon />}
                    onClick={handleDocumentSearch}
                  >
                    Search
                  </Button>
                </Box>
              </Box>
              
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Document Name</TableCell>
                        <TableCell>Repository</TableCell>
                        <TableCell>Last Modified</TableCell>
                        <TableCell>Size</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredDocuments.map((document) => (
                        <TableRow key={document.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <DocumentIcon sx={{ mr: 1, color: 'primary.main' }} />
                              {document.name}
                            </Box>
                          </TableCell>
                          <TableCell>{document.repository}</TableCell>
                          <TableCell>{document.lastModified}</TableCell>
                          <TableCell>{document.size}</TableCell>
                          <TableCell align="right">
                            <IconButton color="primary">
                              <DownloadIcon />
                            </IconButton>
                            <IconButton color="primary">
                              <LinkIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Document Integration
                </Typography>
                <Typography variant="body2" paragraph>
                  Connect to your document management systems to automatically import and link relevant documents to your compliance projects.
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  Supported Systems:
                </Typography>
                <Typography variant="body2">
                  • SharePoint<br />
                  • Google Drive<br />
                  • Dropbox<br />
                  • OneDrive<br />
                  • Box
                </Typography>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Document Categories
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <FolderIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Disaster Recovery Plans"
                      secondary="15 documents"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <FolderIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="FEMA Applications"
                      secondary="8 documents"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <FolderIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Compliance Checklists"
                      secondary="12 documents"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <FolderIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Policies and Procedures"
                      secondary="24 documents"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <FolderIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Training Materials"
                      secondary="10 documents"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
      
      {/* Calendar Integration */}
      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Compliance Calendar
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <FormControl sx={{ minWidth: 120, mr: 2 }}>
                    <InputLabel>View</InputLabel>
                    <Select
                      value={calendarView}
                      label="View"
                      onChange={handleCalendarViewChange}
                      size="small"
                    >
                      <MenuItem value="month">Month</MenuItem>
                      <MenuItem value="week">Week</MenuItem>
                      <MenuItem value="day">Day</MenuItem>
                    </Select>
                  </FormControl>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleOpenEventDialog}
                  >
                    Add Event
                  </Button>
                </Box>
              </Box>
              
              {/* Calendar would be rendered here with a calendar component */}
              {/* For this demo, we'll just show the events in a table */}
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Event</TableCell>
                      <TableCell>Start Date</TableCell>
                      <TableCell>End Date</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Calendar</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {calendarEvents.map((event) => (
                      <TableRow key={event.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <EventIcon sx={{ mr: 1, color: 'primary.main' }} />
                            {event.title}
                          </Box>
                        </TableCell>
                        <TableCell>{event.start}</TableCell>
                        <TableCell>{event.end}</TableCell>
                        <TableCell>
                          <Chip
                            label={event.type}
                            color={
                              event.type === 'deadline' ? 'error' :
                              event.type === 'meeting' ? 'primary' :
                              event.type === 'training' ? 'success' : 'default'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{event.calendar}</TableCell>
                        <TableCell align="right">
                          <IconButton color="primary">
                            <EditIcon />
                          </IconButton>
                          <IconButton color="error">
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Calendar Sources
                </Typography>
                <List dense>
                  {calendarSources.map((source) => (
                    <ListItem key={source.id}>
                      <ListItemIcon>
                        <CalendarIcon color={source.connected ? 'primary' : 'disabled'} />
                      </ListItemIcon>
                      <ListItemText
                        primary={source.name}
                        secondary={source.connected ? `Last sync: ${source.lastSync}` : 'Not connected'}
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={source.connected}
                            color="primary"
                            size="small"
                          />
                        }
                        label=""
                      />
                    </ListItem>
                  ))}
                </List>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<AddIcon />}
                  sx={{ mt: 2 }}
                >
                  Add Calendar Source
                </Button>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Upcoming Deadlines
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <EventIcon color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary="FEMA Application Deadline"
                      secondary="April 20, 2025"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EventIcon color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Document Submission"
                      secondary="May 5, 2025"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EventIcon color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Quarterly Report Due"
                      secondary="June 30, 2025"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EventIcon color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Policy Review Deadline"
                      secondary="July 15, 2025"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
      
      {/* Add Repository Dialog */}
      <Dialog open={repositoryDialogOpen} onClose={handleCloseRepositoryDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Add Document Repository</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Repository Name"
            fullWidth
            variant="outlined"
            value={newRepositoryName}
            onChange={(e) => setNewRepositoryName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Repository URL"
            fullWidth
            variant="outlined"
            value={newRepositoryUrl}
            onChange={(e) => setNewRepositoryUrl(e.target.value)}
            sx={{ mb: 2 }}
          />
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Repository Type</InputLabel>
            <Select
              label="Repository Type"
              value=""
            >
              <MenuItem value="sharepoint">SharePoint</MenuItem>
              <MenuItem value="googledrive">Google Drive</MenuItem>
              <MenuItem value="dropbox">Dropbox</MenuItem>
              <MenuItem value="onedrive">OneDrive</MenuItem>
              <MenuItem value="box">Box</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRepositoryDialog}>Cancel</Button>
          <Button onClick={handleAddRepository} variant="contained">
            Add Repository
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Add Event Dialog */}
      <Dialog open={eventDialogOpen} onClose={handleCloseEventDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Add Calendar Event</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Event Title"
            fullWidth
            variant="outlined"
            value={newEventTitle}
            onChange={(e) => setNewEventTitle(e.target.value)}
            sx={{ mb: 2 }}
          />
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                margin="dense"
                label="Start Date"
                type="date"
                fullWidth
                variant="outlined"
                value={newEventStart}
                onChange={(e) => setNewEventStart(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                margin="dense"
                label="End Date"
                type="date"
                fullWidth
                variant="outlined"
                value={newEventEnd}
                onChange={(e) => setNewEventEnd(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
          <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
            <InputLabel>Event Type</InputLabel>
            <Select
              label="Event Type"
              value={newEventType}
              onChange={(e) => setNewEventType(e.target.value)}
            >
              <MenuItem value="deadline">Deadline</MenuItem>
              <MenuItem value="meeting">Meeting</MenuItem>
              <MenuItem value="training">Training</MenuItem>
              <MenuItem value="exercise">Exercise</MenuItem>
            </Select>
          </FormControl>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Calendar</InputLabel>
            <Select
              label="Calendar"
              value={newEventCalendar}
              onChange={(e) => setNewEventCalendar(e.target.value)}
            >
              <MenuItem value="Compliance">Compliance</MenuItem>
              <MenuItem value="FEMA">FEMA</MenuItem>
              <MenuItem value="Team">Team</MenuItem>
              <MenuItem value="Emergency">Emergency</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEventDialog}>Cancel</Button>
          <Button onClick={handleAddEvent} variant="contained">
            Add Event
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ExternalSystemsIntegration;
