import React, { useState } from 'react';
import {
  Box, Typography, Paper, Grid, Button, TextField,
  Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, Chip, IconButton, Card, CardContent,
  Dialog, DialogTitle, DialogContent, DialogActions,
  FormControl, InputLabel, Select, MenuItem, SelectChangeEvent
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  CloudUpload as UploadIcon,
  Search as SearchIcon
} from '@mui/icons-material';

interface Document {
  id: number;
  name: string;
  type: string;
  uploadDate: string;
  status: string;
  size: string;
}

const Documents: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([
    { id: 1, name: 'Security Policy 2025', type: 'Policy', uploadDate: '2025-03-15', status: 'Active', size: '1.2 MB' },
    { id: 2, name: 'Disaster Recovery Plan', type: 'Procedure', uploadDate: '2025-02-28', status: 'Under Review', size: '3.5 MB' },
    { id: 3, name: 'Employee Handbook', type: 'Policy', uploadDate: '2025-01-10', status: 'Active', size: '2.8 MB' },
    { id: 4, name: 'FEMA Compliance Checklist', type: 'Form', uploadDate: '2025-03-22', status: 'Active', size: '0.5 MB' },
    { id: 5, name: 'Quarterly Compliance Report', type: 'Report', uploadDate: '2025-04-01', status: 'Draft', size: '1.7 MB' },
  ]);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('All');
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState('Policy');
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };
  
  const handleFilterChange = (event: SelectChangeEvent) => {
    setFilterType(event.target.value);
  };
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
    }
  };
  
  const handleTypeChange = (event: SelectChangeEvent) => {
    setDocumentType(event.target.value);
  };
  
  const handleUploadDialogOpen = () => {
    setOpenUploadDialog(true);
  };
  
  const handleUploadDialogClose = () => {
    setOpenUploadDialog(false);
    setSelectedFile(null);
  };
  
  const handleUpload = () => {
    if (!selectedFile) return;
    
    // Simulate adding a new document
    const newDocument: Document = {
      id: documents.length + 1,
      name: selectedFile.name,
      type: documentType,
      uploadDate: new Date().toISOString().split('T')[0],
      status: 'Active',
      size: `${(selectedFile.size / (1024 * 1024)).toFixed(1)} MB`,
    };
    
    setDocuments([...documents, newDocument]);
    handleUploadDialogClose();
  };
  
  const handleDeleteDocument = (id: number) => {
    setDocuments(documents.filter(doc => doc.id !== id));
  };
  
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'All' || doc.type === filterType;
    return matchesSearch && matchesFilter;
  });
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Draft': return 'warning';
      case 'Under Review': return 'info';
      default: return 'default';
    }
  };
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Documents
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel>Document Type</InputLabel>
                  <Select
                    value={filterType}
                    label="Document Type"
                    onChange={handleFilterChange}
                  >
                    <MenuItem value="All">All Types</MenuItem>
                    <MenuItem value="Policy">Policy</MenuItem>
                    <MenuItem value="Procedure">Procedure</MenuItem>
                    <MenuItem value="Form">Form</MenuItem>
                    <MenuItem value="Report">Report</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: { xs: 'flex-start', sm: 'flex-end' } }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleUploadDialogOpen}
                >
                  Upload Document
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
        
        <Grid item xs={12}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Upload Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Size</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredDocuments.map((doc) => (
                  <TableRow key={doc.id}>
                    <TableCell>{doc.name}</TableCell>
                    <TableCell>{doc.type}</TableCell>
                    <TableCell>{doc.uploadDate}</TableCell>
                    <TableCell>
                      <Chip
                        label={doc.status}
                        color={getStatusColor(doc.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{doc.size}</TableCell>
                    <TableCell align="right">
                      <IconButton size="small" color="primary">
                        <ViewIcon />
                      </IconButton>
                      <IconButton size="small" color="primary">
                        <EditIcon />
                      </IconButton>
                      <IconButton 
                        size="small" 
                        color="error"
                        onClick={() => handleDeleteDocument(doc.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      </Grid>
      
      {/* Upload Dialog */}
      <Dialog open={openUploadDialog} onClose={handleUploadDialogClose}>
        <DialogTitle>Upload Document</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1, pb: 2 }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
              fullWidth
              sx={{ mb: 2 }}
            >
              Select File
              <input
                type="file"
                hidden
                accept=".pdf,.doc,.docx,.txt,.xlsx"
                onChange={handleFileChange}
              />
            </Button>
            
            {selectedFile && (
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Selected: {selectedFile.name}
              </Typography>
            )}
            
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Document Type</InputLabel>
              <Select
                value={documentType}
                label="Document Type"
                onChange={handleTypeChange}
              >
                <MenuItem value="Policy">Policy</MenuItem>
                <MenuItem value="Procedure">Procedure</MenuItem>
                <MenuItem value="Form">Form</MenuItem>
                <MenuItem value="Report">Report</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleUploadDialogClose}>Cancel</Button>
          <Button 
            onClick={handleUpload} 
            variant="contained" 
            disabled={!selectedFile}
          >
            Upload
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Documents;
