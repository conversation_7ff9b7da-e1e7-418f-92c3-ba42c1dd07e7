# ComplianceMax Application Deployment Guide

## Introduction

This guide provides detailed instructions for deploying the ComplianceMax application in various environments. Follow these steps to set up and configure the application for your organization.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Architecture Overview](#architecture-overview)
3. [Frontend Deployment](#frontend-deployment)
4. [Backend Deployment](#backend-deployment)
5. [Database Setup](#database-setup)
6. [External Integrations Configuration](#external-integrations-configuration)
7. [Security Considerations](#security-considerations)
8. [Monitoring and Maintenance](#monitoring-and-maintenance)
9. [Troubleshooting](#troubleshooting)

## System Requirements

### Production Environment

- **Frontend Server**:
  - Node.js 16.x or higher
  - 2 CPU cores minimum, 4 recommended
  - 4GB RAM minimum, 8GB recommended
  - 20GB storage minimum

- **Backend Server**:
  - Python 3.10 or higher
  - 4 CPU cores minimum, 8 recommended
  - 8GB RAM minimum, 16GB recommended
  - 50GB storage minimum

- **Database Server**:
  - PostgreSQL 14.x or higher
  - 4 CPU cores minimum, 8 recommended
  - 8GB RAM minimum, 16GB recommended
  - 100GB storage minimum, with backup capacity

- **Network Requirements**:
  - HTTPS with valid SSL certificate
  - Outbound access for external integrations
  - Firewall rules for appropriate ports

### Development Environment

- Node.js 16.x or higher
- Python 3.10 or higher
- PostgreSQL 14.x or higher
- Git
- Docker and Docker Compose (optional but recommended)

## Architecture Overview

ComplianceMax follows a modern microservices architecture:

1. **React Frontend**: Single-page application built with React and Material-UI
2. **FastAPI Backend**: RESTful API service built with Python FastAPI
3. **PostgreSQL Database**: Relational database for persistent storage
4. **Policy Matcher Service**: Specialized service for policy matching functionality
5. **Integration Services**: Connectors for external systems (FEMA, document repositories, calendars)

## Frontend Deployment

### Building the Frontend

1. Navigate to the frontend directory:
   ```bash
   cd /path/to/compliancemax/frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a production build:
   ```bash
   npm run build
   ```

4. The build output will be in the `build` directory

### Deploying to a Web Server

#### Option 1: Nginx (Recommended)

1. Install Nginx:
   ```bash
   sudo apt update
   sudo apt install nginx
   ```

2. Configure Nginx:
   ```bash
   sudo nano /etc/nginx/sites-available/compliancemax
   ```

3. Add the following configuration:
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           root /path/to/compliancemax/frontend/build;
           try_files $uri $uri/ /index.html;
           
           # Cache static assets
           location ~* \.(js|css|png|jpg|jpeg|gif|ico)$ {
               expires 30d;
               add_header Cache-Control "public, no-transform";
           }
       }
       
       # Proxy API requests to backend
       location /api {
           proxy_pass http://localhost:8000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

4. Enable the site:
   ```bash
   sudo ln -s /etc/nginx/sites-available/compliancemax /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

5. Set up SSL with Let's Encrypt:
   ```bash
   sudo apt install certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

#### Option 2: Docker

1. Create a Dockerfile in the frontend directory:
   ```dockerfile
   FROM node:16-alpine as build
   WORKDIR /app
   COPY package*.json ./
   RUN npm install
   COPY . .
   RUN npm run build

   FROM nginx:alpine
   COPY --from=build /app/build /usr/share/nginx/html
   COPY nginx.conf /etc/nginx/conf.d/default.conf
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. Create nginx.conf:
   ```nginx
   server {
       listen 80;
       
       location / {
           root /usr/share/nginx/html;
           index index.html index.htm;
           try_files $uri $uri/ /index.html;
       }
       
       # Proxy API requests to backend
       location /api {
           proxy_pass http://backend:8000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

3. Build and run the Docker container:
   ```bash
   docker build -t compliancemax-frontend .
   docker run -d -p 80:80 compliancemax-frontend
   ```

## Backend Deployment

### Setting Up the Backend

1. Navigate to the backend directory:
   ```bash
   cd /path/to/compliancemax/backend
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Configure environment variables:
   ```bash
   cp .env.example .env
   nano .env
   ```
   
   Update the following variables:
   ```
   DATABASE_URL=postgresql://user:password@localhost/compliancemax
   SECRET_KEY=your-secure-secret-key
   ALLOWED_ORIGINS=https://your-domain.com
   ```

### Running with Gunicorn (Production)

1. Install Gunicorn:
   ```bash
   pip install gunicorn
   ```

2. Create a systemd service:
   ```bash
   sudo nano /etc/systemd/system/compliancemax.service
   ```

3. Add the following configuration:
   ```ini
   [Unit]
   Description=ComplianceMax Backend
   After=network.target

   [Service]
   User=ubuntu
   Group=ubuntu
   WorkingDirectory=/path/to/compliancemax/backend
   Environment="PATH=/path/to/compliancemax/backend/venv/bin"
   EnvironmentFile=/path/to/compliancemax/backend/.env
   ExecStart=/path/to/compliancemax/backend/venv/bin/gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000

   [Install]
   WantedBy=multi-user.target
   ```

4. Enable and start the service:
   ```bash
   sudo systemctl enable compliancemax
   sudo systemctl start compliancemax
   ```

### Docker Deployment

1. Create a Dockerfile in the backend directory:
   ```dockerfile
   FROM python:3.10-slim

   WORKDIR /app

   COPY requirements.txt .
   RUN pip install --no-cache-dir -r requirements.txt

   COPY . .

   CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
   ```

2. Build and run the Docker container:
   ```bash
   docker build -t compliancemax-backend .
   docker run -d -p 8000:8000 --env-file .env compliancemax-backend
   ```

## Database Setup

### PostgreSQL Installation

1. Install PostgreSQL:
   ```bash
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   ```

2. Create a database and user:
   ```bash
   sudo -u postgres psql
   ```

3. In the PostgreSQL prompt:
   ```sql
   CREATE DATABASE compliancemax;
   CREATE USER compliancemax_user WITH ENCRYPTED PASSWORD 'your-secure-password';
   GRANT ALL PRIVILEGES ON DATABASE compliancemax TO compliancemax_user;
   \q
   ```

### Database Migrations

1. Run database migrations:
   ```bash
   cd /path/to/compliancemax/backend
   source venv/bin/activate
   alembic upgrade head
   ```

### Backup and Restore

1. Set up regular backups:
   ```bash
   sudo nano /etc/cron.daily/backup-compliancemax-db
   ```

2. Add the following script:
   ```bash
   #!/bin/bash
   BACKUP_DIR="/path/to/backups"
   TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
   BACKUP_FILE="$BACKUP_DIR/compliancemax_$TIMESTAMP.sql"

   # Create backup
   pg_dump -U compliancemax_user -d compliancemax > $BACKUP_FILE

   # Compress backup
   gzip $BACKUP_FILE

   # Remove backups older than 30 days
   find $BACKUP_DIR -name "compliancemax_*.sql.gz" -mtime +30 -delete
   ```

3. Make the script executable:
   ```bash
   sudo chmod +x /etc/cron.daily/backup-compliancemax-db
   ```

## External Integrations Configuration

### FEMA Database Integration

1. Register for FEMA API access at https://www.fema.gov/api
2. Add your API key to the .env file:
   ```
   FEMA_API_KEY=your-api-key
   FEMA_API_URL=https://www.fema.gov/api/open/v1
   ```

### Document Management Integration

Configure the following variables in the .env file for each document repository:

```
# SharePoint
SHAREPOINT_CLIENT_ID=your-client-id
SHAREPOINT_CLIENT_SECRET=your-client-secret
SHAREPOINT_TENANT_ID=your-tenant-id
SHAREPOINT_SITE_URL=https://your-org.sharepoint.com/sites/compliance

# Google Drive
GOOGLE_DRIVE_CLIENT_ID=your-client-id
GOOGLE_DRIVE_CLIENT_SECRET=your-client-secret
GOOGLE_DRIVE_REDIRECT_URI=https://your-domain.com/api/integrations/google/callback
```

### Calendar Integration

Configure the following variables in the .env file for calendar integrations:

```
# Google Calendar
GOOGLE_CALENDAR_CLIENT_ID=your-client-id
GOOGLE_CALENDAR_CLIENT_SECRET=your-client-secret
GOOGLE_CALENDAR_REDIRECT_URI=https://your-domain.com/api/integrations/google-calendar/callback

# Microsoft Outlook
OUTLOOK_CLIENT_ID=your-client-id
OUTLOOK_CLIENT_SECRET=your-client-secret
OUTLOOK_REDIRECT_URI=https://your-domain.com/api/integrations/outlook/callback
```

## Security Considerations

### API Security

1. Ensure all API endpoints are protected with proper authentication
2. Use HTTPS for all communications
3. Implement rate limiting to prevent abuse
4. Validate all input data to prevent injection attacks

### Authentication

1. Configure authentication settings in the .env file:
   ```
   JWT_SECRET=your-secure-jwt-secret
   JWT_ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   REFRESH_TOKEN_EXPIRE_DAYS=7
   ```

2. For production, consider integrating with OAuth providers:
   ```
   OAUTH_GOOGLE_CLIENT_ID=your-client-id
   OAUTH_GOOGLE_CLIENT_SECRET=your-client-secret
   OAUTH_MICROSOFT_CLIENT_ID=your-client-id
   OAUTH_MICROSOFT_CLIENT_SECRET=your-client-secret
   ```

### Data Protection

1. Ensure sensitive data is encrypted at rest:
   ```
   DATABASE_ENCRYPTION_KEY=your-secure-encryption-key
   ```

2. Configure data retention policies:
   ```
   DATA_RETENTION_DAYS=365
   ```

## Monitoring and Maintenance

### Logging

1. Configure logging in the .env file:
   ```
   LOG_LEVEL=INFO
   LOG_FORMAT=json
   LOG_FILE=/var/log/compliancemax/app.log
   ```

2. Set up log rotation:
   ```bash
   sudo nano /etc/logrotate.d/compliancemax
   ```

3. Add the following configuration:
   ```
   /var/log/compliancemax/*.log {
       daily
       missingok
       rotate 14
       compress
       delaycompress
       notifempty
       create 0640 ubuntu ubuntu
   }
   ```

### Health Checks

1. The backend provides a health check endpoint at `/api/health`
2. Set up monitoring to regularly check this endpoint
3. Configure alerts for any failures

### Updates and Maintenance

1. Create a maintenance script:
   ```bash
   sudo nano /usr/local/bin/update-compliancemax
   ```

2. Add the following content:
   ```bash
   #!/bin/bash
   
   # Update frontend
   cd /path/to/compliancemax/frontend
   git pull
   npm install
   npm run build
   
   # Update backend
   cd /path/to/compliancemax/backend
   git pull
   source venv/bin/activate
   pip install -r requirements.txt
   alembic upgrade head
   
   # Restart services
   sudo systemctl restart compliancemax
   ```

3. Make the script executable:
   ```bash
   sudo chmod +x /usr/local/bin/update-compliancemax
   ```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check database credentials in .env file
   - Verify PostgreSQL is running: `sudo systemctl status postgresql`
   - Check network connectivity: `ping database-host`

2. **API Errors**:
   - Check backend logs: `sudo journalctl -u compliancemax`
   - Verify backend service is running: `sudo systemctl status compliancemax`
   - Test API directly: `curl -v http://localhost:8000/api/health`

3. **Frontend Issues**:
   - Check browser console for JavaScript errors
   - Verify Nginx configuration: `sudo nginx -t`
   - Check Nginx logs: `sudo tail -f /var/log/nginx/error.log`

### Support Resources

- Documentation: https://docs.compliancemax.com
- GitHub Repository: https://github.com/compliancemax/compliancemax
- Support Email: <EMAIL>
- Community Forum: https://community.compliancemax.com

---

For additional assistance, please contact our support <NAME_EMAIL> or call 1-800-COMPLY-MAX.
