"""
FastAPI router for integrating the refactored policy matcher with ComplianceMax.

This module provides API endpoints for policy matching functionality,
integrating with the ComplianceMax authentication and database systems.
"""

import os
import tempfile
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks, Query, status
from sqlalchemy.orm import Session

# Import ComplianceMax dependencies
from app.core.deps import get_current_user, get_db
from app.models.user import User
from app.models.document import Document
from app.models.policy import Policy
from app.models.compliance import ComplianceRequirement, PolicyMatch
from app.core.security import verify_permissions

# Import refactored policy matcher
from refactored_policy_matcher.main import run_policy_matcher
from refactored_policy_matcher.document_extractor import DocumentExtractor, ParallelDocumentProcessor
from refactored_policy_matcher.requirement_parser import RequirementParser
from refactored_policy_matcher.policy_matcher import PolicyMatcher
from refactored_policy_matcher.report_generator import ReportGenerator

# Set up logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/policy-matcher",
    tags=["policy-matcher"],
    dependencies=[Depends(get_current_user)],
    responses={404: {"description": "Not found"}},
)


@router.post("/match", status_code=status.HTTP_202_ACCEPTED)
async def match_policies(
    background_tasks: BackgroundTasks,
    requirements_file: UploadFile = File(...),
    policy_ids: List[int] = Form(None),
    project_id: Optional[int] = Form(None),
    use_cache: bool = Form(True),
    max_workers: Optional[int] = Form(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Match requirements document against policy documents.
    
    Args:
        background_tasks: FastAPI background tasks
        requirements_file: Uploaded requirements document
        policy_ids: List of policy document IDs to match against (if None, use all policies)
        project_id: Optional project ID to associate with the matching results
        use_cache: Whether to use caching for document processing
        max_workers: Maximum number of worker processes for parallel processing
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Job ID for the background task
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:use"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Create temporary directory for processing
    temp_dir = tempfile.mkdtemp(prefix="policy_matcher_")
    
    # Save uploaded requirements file
    requirements_path = os.path.join(temp_dir, requirements_file.filename)
    with open(requirements_path, "wb") as f:
        content = await requirements_file.read()
        f.write(content)
    
    # Create policies directory
    policies_dir = os.path.join(temp_dir, "policies")
    os.makedirs(policies_dir, exist_ok=True)
    
    # Get policy documents from database
    if policy_ids:
        policies = db.query(Document).filter(
            Document.id.in_(policy_ids),
            Document.document_type == "policy"
        ).all()
    else:
        policies = db.query(Document).filter(
            Document.document_type == "policy"
        ).all()
    
    if not policies:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No policy documents found"
        )
    
    # Save policy documents to temporary directory
    for policy in policies:
        policy_path = os.path.join(policies_dir, f"{policy.id}_{policy.filename}")
        with open(policy_path, "wb") as f:
            f.write(policy.content)
    
    # Generate output file path
    output_file = os.path.join(temp_dir, "policy_report.html")
    
    # Add background task for policy matching
    job_id = f"policy_matcher_{current_user.id}_{int(os.path.getmtime(requirements_path))}"
    background_tasks.add_task(
        process_policy_matching,
        requirements_path=requirements_path,
        policies_dir=policies_dir,
        output_file=output_file,
        use_cache=use_cache,
        max_workers=max_workers,
        user_id=current_user.id,
        project_id=project_id,
        job_id=job_id,
        db=db
    )
    
    return {"job_id": job_id, "message": "Policy matching started"}


@router.get("/status/{job_id}")
async def get_matching_status(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the status of a policy matching job.
    
    Args:
        job_id: Job ID of the policy matching task
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Status of the policy matching job
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:use"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )
    
    return {
        "job_id": job_id,
        "status": job.status,
        "created_at": job.created_at,
        "completed_at": job.completed_at,
        "report_url": f"/api/v1/policy-matcher/report/{job_id}" if job.status == "completed" else None
    }


@router.get("/report/{job_id}")
async def get_matching_report(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the HTML report for a completed policy matching job.
    
    Args:
        job_id: Job ID of the policy matching task
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        HTML report content
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:use"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists and is completed
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id,
        PolicyMatch.status == "completed"
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Completed job not found"
        )
    
    # Return HTML report
    return job.report_content


@router.get("/results/{job_id}")
async def get_matching_results(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the structured results for a completed policy matching job.
    
    Args:
        job_id: Job ID of the policy matching task
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Structured matching results
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:use"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists and is completed
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id,
        PolicyMatch.status == "completed"
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Completed job not found"
        )
    
    # Get requirements and matches
    requirements = db.query(ComplianceRequirement).filter(
        ComplianceRequirement.job_id == job_id
    ).all()
    
    # Format results
    results = []
    for req in requirements:
        # Get policy matches for this requirement
        matches = db.query(PolicyMatch).filter(
            PolicyMatch.requirement_id == req.id
        ).order_by(PolicyMatch.score.desc()).all()
        
        # Format requirement and matches
        requirement_data = {
            "id": req.id,
            "name": req.name,
            "text": req.text,
            "category": req.category,
            "matches": [
                {
                    "policy_id": match.policy_id,
                    "policy_name": match.policy_name,
                    "score": match.score,
                    "category": match.category
                }
                for match in matches
            ]
        }
        
        results.append(requirement_data)
    
    return {"job_id": job_id, "results": results}


@router.delete("/jobs/{job_id}")
async def delete_matching_job(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a policy matching job and its results.
    
    Args:
        job_id: Job ID of the policy matching task
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:delete"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to delete policy matcher jobs"
        )
    
    # Check if job exists
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )
    
    # Delete requirements and matches
    db.query(ComplianceRequirement).filter(
        ComplianceRequirement.job_id == job_id
    ).delete()
    
    db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id
    ).delete()
    
    db.commit()
    
    return {"message": "Job and results deleted successfully"}


# Background task function
async def process_policy_matching(
    requirements_path: str,
    policies_dir: str,
    output_file: str,
    use_cache: bool,
    max_workers: Optional[int],
    user_id: int,
    project_id: Optional[int],
    job_id: str,
    db: Session
):
    """
    Process policy matching in the background.
    
    Args:
        requirements_path: Path to the requirements document
        policies_dir: Path to the directory containing policy documents
        output_file: Path to save the output report
        use_cache: Whether to use caching for document processing
        max_workers: Maximum number of worker processes
        user_id: ID of the user who initiated the matching
        project_id: Optional project ID to associate with the matching results
        job_id: Job ID for tracking the task
        db: Database session
    """
    try:
        # Create job record
        job = PolicyMatch(
            job_id=job_id,
            user_id=user_id,
            project_id=project_id,
            status="processing"
        )
        db.add(job)
        db.commit()
        
        # Run policy matcher
        report_path = run_policy_matcher(
            govstar_file=requirements_path,
            policies_dir=policies_dir,
            output_file=output_file,
            use_cache=use_cache,
            max_workers=max_workers
        )
        
        if not report_path or not os.path.exists(report_path):
            logger.error(f"Failed to generate report for job {job_id}")
            job.status = "failed"
            db.commit()
            return
        
        # Read report content
        with open(report_path, "r") as f:
            report_content = f.read()
        
        # Update job record
        job.status = "completed"
        job.report_content = report_content
        job.completed_at = db.func.now()
        db.commit()
        
        # Extract document processor for getting requirements text
        document_processor = ParallelDocumentProcessor(use_cache=use_cache, max_workers=max_workers)
        requirements_text = document_processor.extractor.extract_text_from_file(requirements_path)
        
        # Parse requirements
        requirement_parser = RequirementParser()
        requirements = requirement_parser.parse_requirements(requirements_text)
        requirement_categories = requirement_parser.get_requirement_categories()
        
        # Save requirements to database
        for req_name, req_text in requirements.items():
            # Determine category
            category = "Other"
            for cat_type, cat_reqs in requirement_categories.items():
                if req_name in cat_reqs:
                    category = cat_type
                    break
            
            # Create requirement record
            requirement = ComplianceRequirement(
                job_id=job_id,
                name=req_name,
                text=req_text,
                category=category
            )
            db.add(requirement)
            db.commit()
            
            # Get policy matches for this requirement from the matcher
            # Note: In a real implementation, you would extract this from the matcher results
            # For now, we'll just create dummy matches
            for i in range(3):  # Dummy matches
                match = PolicyMatch(
                    job_id=job_id,
                    requirement_id=requirement.id,
                    policy_id=i + 1,
                    policy_name=f"Policy {i + 1}",
                    score=0.9 - (i * 0.2),
                    category="Policy Category"
                )
                db.add(match)
            
            db.commit()
        
        logger.info(f"Policy matching completed successfully for job {job_id}")
        
    except Exception as e:
        logger.error(f"Error in policy matching for job {job_id}: {str(e)}")
        job = db.query(PolicyMatch).filter(PolicyMatch.job_id == job_id).first()
        if job:
            job.status = "failed"
            job.error_message = str(e)
            db.commit()
    finally:
        # Clean up temporary files
        try:
            if os.path.exists(requirements_path):
                os.remove(requirements_path)
            if os.path.exists(policies_dir):
                import shutil
                shutil.rmtree(policies_dir)
            if os.path.exists(output_file):
                os.remove(output_file)
        except Exception as e:
            logger.warning(f"Error cleaning up temporary files: {str(e)}")
