"""
Database integration module for the Policy Matcher system.

This module provides integration between the refactored policy matcher
and the ComplianceMax database models.
"""

import logging
from typing import Dict, List, Tuple, Optional, Any
from sqlalchemy.orm import Session

# Import ComplianceMax models
from app.models.document import Document
from app.models.policy import Policy
from app.models.compliance import ComplianceRequirement, PolicyMatch
from app.models.user import User
from app.models.project import Project

# Import refactored policy matcher
from refactored_policy_matcher.document_extractor import DocumentExtractor
from refactored_policy_matcher.requirement_parser import RequirementParser
from refactored_policy_matcher.policy_matcher import PolicyMatcher

# Set up logger
logger = logging.getLogger(__name__)


class DatabaseIntegration:
    """
    Handles integration between the policy matcher and database models.
    """
    
    def __init__(self, db: Session):
        """
        Initialize the database integration.
        
        Args:
            db: Database session
        """
        self.db = db
    
    def get_policies_from_database(
        self, 
        policy_ids: Optional[List[int]] = None,
        project_id: Optional[int] = None,
        category: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Retrieve policy documents from the database.
        
        Args:
            policy_ids: Optional list of policy IDs to retrieve
            project_id: Optional project ID to filter policies
            category: Optional category to filter policies
            
        Returns:
            Dictionary mapping policy names to policy text
        """
        query = self.db.query(Document).filter(Document.document_type == "policy")
        
        # Apply filters
        if policy_ids:
            query = query.filter(Document.id.in_(policy_ids))
        
        if project_id:
            query = query.filter(Document.project_id == project_id)
        
        if category:
            query = query.filter(Document.category == category)
        
        # Execute query
        policies = query.all()
        
        # Convert to dictionary
        policy_dict = {}
        for policy in policies:
            # Extract text from binary content
            extractor = DocumentExtractor()
            text = extractor.extract_text_from_file(policy.file_path)
            
            if text:
                policy_dict[f"{policy.id}_{policy.filename}"] = text
        
        logger.info(f"Retrieved {len(policy_dict)} policies from database")
        return policy_dict
    
    def save_requirements_to_database(
        self,
        requirements: Dict[str, str],
        requirement_categories: Dict[str, List[str]],
        job_id: str,
        user_id: int,
        project_id: Optional[int] = None
    ) -> Dict[str, int]:
        """
        Save parsed requirements to the database.
        
        Args:
            requirements: Dictionary mapping requirement names to requirement text
            requirement_categories: Dictionary mapping category types to lists of requirement names
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            project_id: Optional project ID to associate with the requirements
            
        Returns:
            Dictionary mapping requirement names to requirement IDs
        """
        requirement_ids = {}
        
        for req_name, req_text in requirements.items():
            # Determine category
            category = "Other"
            for cat_type, cat_reqs in requirement_categories.items():
                if req_name in cat_reqs:
                    category = cat_type
                    break
            
            # Create requirement record
            requirement = ComplianceRequirement(
                job_id=job_id,
                user_id=user_id,
                project_id=project_id,
                name=req_name,
                text=req_text,
                category=category
            )
            
            self.db.add(requirement)
            self.db.flush()  # Get ID without committing
            
            requirement_ids[req_name] = requirement.id
        
        self.db.commit()
        logger.info(f"Saved {len(requirement_ids)} requirements to database")
        
        return requirement_ids
    
    def save_matches_to_database(
        self,
        matches: Dict[str, List[Tuple[str, float]]],
        requirement_ids: Dict[str, int],
        job_id: str,
        user_id: int,
        project_id: Optional[int] = None
    ) -> None:
        """
        Save policy matches to the database.
        
        Args:
            matches: Dictionary mapping requirement names to lists of (policy_name, score) tuples
            requirement_ids: Dictionary mapping requirement names to requirement IDs
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            project_id: Optional project ID to associate with the matches
        """
        match_count = 0
        
        for req_name, policy_matches in matches.items():
            if req_name not in requirement_ids:
                logger.warning(f"Requirement '{req_name}' not found in requirement_ids")
                continue
            
            requirement_id = requirement_ids[req_name]
            
            for policy_name, score in policy_matches:
                # Extract policy ID from policy name (format: "{id}_{filename}")
                try:
                    policy_id = int(policy_name.split('_')[0])
                except (ValueError, IndexError):
                    logger.warning(f"Could not extract policy ID from '{policy_name}'")
                    continue
                
                # Get policy category
                policy = self.db.query(Document).filter(Document.id == policy_id).first()
                if not policy:
                    logger.warning(f"Policy with ID {policy_id} not found in database")
                    continue
                
                # Create match record
                match = PolicyMatch(
                    job_id=job_id,
                    user_id=user_id,
                    project_id=project_id,
                    requirement_id=requirement_id,
                    policy_id=policy_id,
                    policy_name=policy.filename,
                    score=score,
                    category=policy.category or "Uncategorized"
                )
                
                self.db.add(match)
                match_count += 1
        
        self.db.commit()
        logger.info(f"Saved {match_count} policy matches to database")
    
    def save_report_to_database(
        self,
        report_content: str,
        job_id: str,
        user_id: int,
        project_id: Optional[int] = None
    ) -> None:
        """
        Save the HTML report to the database.
        
        Args:
            report_content: HTML content of the report
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            project_id: Optional project ID to associate with the report
        """
        # Update job record with report content
        job = self.db.query(PolicyMatch).filter(
            PolicyMatch.job_id == job_id,
            PolicyMatch.user_id == user_id
        ).first()
        
        if job:
            job.report_content = report_content
            job.status = "completed"
            job.completed_at = self.db.func.now()
            
            self.db.commit()
            logger.info(f"Saved report for job {job_id} to database")
        else:
            logger.warning(f"Job {job_id} not found in database")
    
    def get_matching_results(
        self,
        job_id: str,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Retrieve matching results from the database.
        
        Args:
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            
        Returns:
            Dictionary containing matching results
        """
        # Get requirements
        requirements = self.db.query(ComplianceRequirement).filter(
            ComplianceRequirement.job_id == job_id,
            ComplianceRequirement.user_id == user_id
        ).all()
        
        results = []
        for req in requirements:
            # Get policy matches for this requirement
            matches = self.db.query(PolicyMatch).filter(
                PolicyMatch.requirement_id == req.id
            ).order_by(PolicyMatch.score.desc()).all()
            
            # Format requirement and matches
            requirement_data = {
                "id": req.id,
                "name": req.name,
                "text": req.text,
                "category": req.category,
                "matches": [
                    {
                        "policy_id": match.policy_id,
                        "policy_name": match.policy_name,
                        "score": match.score,
                        "category": match.category
                    }
                    for match in matches
                ]
            }
            
            results.append(requirement_data)
        
        return {
            "job_id": job_id,
            "results": results,
            "requirement_count": len(requirements)
        }
    
    def get_job_status(
        self,
        job_id: str,
        user_id: int
    ) -> Dict[str, Any]:
        """
        Get the status of a matching job.
        
        Args:
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            
        Returns:
            Dictionary containing job status information
        """
        job = self.db.query(PolicyMatch).filter(
            PolicyMatch.job_id == job_id,
            PolicyMatch.user_id == user_id
        ).first()
        
        if not job:
            return {"status": "not_found"}
        
        return {
            "job_id": job_id,
            "status": job.status,
            "created_at": job.created_at,
            "completed_at": job.completed_at,
            "error_message": job.error_message
        }
    
    def delete_job(
        self,
        job_id: str,
        user_id: int
    ) -> bool:
        """
        Delete a matching job and its results.
        
        Args:
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete requirements
            self.db.query(ComplianceRequirement).filter(
                ComplianceRequirement.job_id == job_id,
                ComplianceRequirement.user_id == user_id
            ).delete()
            
            # Delete matches
            self.db.query(PolicyMatch).filter(
                PolicyMatch.job_id == job_id,
                PolicyMatch.user_id == user_id
            ).delete()
            
            self.db.commit()
            logger.info(f"Deleted job {job_id} and its results")
            
            return True
        except Exception as e:
            logger.error(f"Error deleting job {job_id}: {str(e)}")
            self.db.rollback()
            return False
