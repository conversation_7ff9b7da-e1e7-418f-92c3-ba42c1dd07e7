import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Grid, Card, CardContent,
  FormControl, InputLabel, Select, MenuItem, SelectChangeEvent
} from '@mui/material';
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell, Scatter, ScatterChart, ZAxis, Radar, RadarChart, 
  PolarGrid, PolarAngleAxis, PolarRadiusAxis, RadialBarChart, RadialBar,
  Treemap, LineChart, Line
} from 'recharts';

// Sample data - in a real app, this would come from an API
const complianceData = [
  { category: 'Debris Removal', compliant: 85, nonCompliant: 15, total: 100 },
  { category: 'Emergency Protective Measures', compliant: 92, nonCompliant: 8, total: 100 },
  { category: 'Roads & Bridges', compliant: 78, nonCompliant: 22, total: 100 },
  { category: 'Water Control Facilities', compliant: 65, nonCompliant: 35, total: 100 },
  { category: 'Buildings & Equipment', compliant: 88, nonCompliant: 12, total: 100 },
  { category: 'Utilities', compliant: 72, nonCompliant: 28, total: 100 },
  { category: 'Parks & Recreation', compliant: 81, nonCompliant: 19, total: 100 },
];

const policyMatchData = [
  { name: 'High Confidence', value: 42 },
  { name: 'Medium Confidence', value: 28 },
  { name: 'Low Confidence', value: 15 },
  { name: 'No Match', value: 8 },
];

const heatmapData = [
  { x: 'Policy 1', y: 'Req 1', value: 90 },
  { x: 'Policy 1', y: 'Req 2', value: 45 },
  { x: 'Policy 1', y: 'Req 3', value: 75 },
  { x: 'Policy 2', y: 'Req 1', value: 30 },
  { x: 'Policy 2', y: 'Req 2', value: 85 },
  { x: 'Policy 2', y: 'Req 3', value: 60 },
  { x: 'Policy 3', y: 'Req 1', value: 65 },
  { x: 'Policy 3', y: 'Req 2', value: 15 },
  { x: 'Policy 3', y: 'Req 3', value: 95 },
];

const trendData = [
  { month: 'Jan', compliance: 75 },
  { month: 'Feb', compliance: 78 },
  { month: 'Mar', compliance: 82 },
  { month: 'Apr', compliance: 85 },
  { month: 'May', compliance: 83 },
  { month: 'Jun', compliance: 87 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const DataVisualization: React.FC = () => {
  const [chartType, setChartType] = useState('bar');
  const [dataType, setDataType] = useState('compliance');
  const [timeRange, setTimeRange] = useState('6m');

  const handleChartTypeChange = (event: SelectChangeEvent) => {
    setChartType(event.target.value);
  };

  const handleDataTypeChange = (event: SelectChangeEvent) => {
    setDataType(event.target.value);
  };

  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    setTimeRange(event.target.value);
  };

  // Function to get color based on value (for heatmap)
  const getHeatmapColor = (value: number) => {
    if (value >= 80) return '#00C49F';
    if (value >= 60) return '#FFBB28';
    if (value >= 40) return '#FF8042';
    return '#FF0000';
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Data Visualization
      </Typography>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel>Chart Type</InputLabel>
              <Select
                value={chartType}
                label="Chart Type"
                onChange={handleChartTypeChange}
              >
                <MenuItem value="bar">Bar Chart</MenuItem>
                <MenuItem value="pie">Pie Chart</MenuItem>
                <MenuItem value="radar">Radar Chart</MenuItem>
                <MenuItem value="heatmap">Heatmap</MenuItem>
                <MenuItem value="trend">Trend Analysis</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel>Data Type</InputLabel>
              <Select
                value={dataType}
                label="Data Type"
                onChange={handleDataTypeChange}
              >
                <MenuItem value="compliance">Compliance by Category</MenuItem>
                <MenuItem value="matching">Policy Matching Results</MenuItem>
                <MenuItem value="coverage">Policy Coverage</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={handleTimeRangeChange}
              >
                <MenuItem value="1m">Last Month</MenuItem>
                <MenuItem value="3m">Last 3 Months</MenuItem>
                <MenuItem value="6m">Last 6 Months</MenuItem>
                <MenuItem value="1y">Last Year</MenuItem>
                <MenuItem value="all">All Time</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, height: 500 }}>
            {/* Bar Chart */}
            {chartType === 'bar' && dataType === 'compliance' && (
              <Box sx={{ height: '100%', width: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Compliance by Category
                </Typography>
                <ResponsiveContainer width="100%" height="90%">
                  <BarChart
                    data={complianceData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="compliant" stackId="a" fill="#00C49F" name="Compliant" />
                    <Bar dataKey="nonCompliant" stackId="a" fill="#FF8042" name="Non-Compliant" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            )}

            {/* Pie Chart */}
            {chartType === 'pie' && dataType === 'matching' && (
              <Box sx={{ height: '100%', width: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Policy Matching Results
                </Typography>
                <ResponsiveContainer width="100%" height="90%">
                  <PieChart>
                    <Pie
                      data={policyMatchData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={150}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {policyMatchData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            )}

            {/* Radar Chart */}
            {chartType === 'radar' && dataType === 'coverage' && (
              <Box sx={{ height: '100%', width: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Policy Coverage by Category
                </Typography>
                <ResponsiveContainer width="100%" height="90%">
                  <RadarChart cx="50%" cy="50%" outerRadius="80%" data={complianceData}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="category" />
                    <PolarRadiusAxis angle={30} domain={[0, 100]} />
                    <Radar name="Compliant" dataKey="compliant" stroke="#00C49F" fill="#00C49F" fillOpacity={0.6} />
                    <Tooltip />
                    <Legend />
                  </RadarChart>
                </ResponsiveContainer>
              </Box>
            )}

            {/* Heatmap (using ScatterChart) */}
            {chartType === 'heatmap' && (
              <Box sx={{ height: '100%', width: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Policy-Requirement Match Heatmap
                </Typography>
                <ResponsiveContainer width="100%" height="90%">
                  <ScatterChart
                    margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="x" type="category" name="Policy" />
                    <YAxis dataKey="y" type="category" name="Requirement" />
                    <ZAxis dataKey="value" range={[0, 500]} />
                    <Tooltip cursor={{ strokeDasharray: '3 3' }} formatter={(value) => [`${value}% Match`, '']} />
                    <Scatter
                      data={heatmapData}
                      fill="#8884d8"
                      shape={(props) => {
                        const { cx, cy, payload } = props;
                        return (
                          <rect
                            x={cx - 15}
                            y={cy - 15}
                            width={30}
                            height={30}
                            fill={getHeatmapColor(payload.value)}
                            fillOpacity={0.8}
                          />
                        );
                      }}
                    />
                  </ScatterChart>
                </ResponsiveContainer>
              </Box>
            )}

            {/* Trend Analysis */}
            {chartType === 'trend' && (
              <Box sx={{ height: '100%', width: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Compliance Trend Analysis
                </Typography>
                <ResponsiveContainer width="100%" height="90%">
                  <LineChart
                    data={trendData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="compliance" stroke="#00C49F" activeDot={{ r: 8 }} />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Compliance Summary
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={[
                      { name: 'Compliant', value: 82 },
                      { name: 'Non-Compliant', value: 18 }
                    ]}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    <Cell fill="#00C49F" />
                    <Cell fill="#FF8042" />
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
              <Typography variant="body2" color="text.secondary">
                Overall compliance rate across all categories.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Policy Gaps
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={[
                    { name: 'Security Policy', gap: 22 },
                    { name: 'Data Protection', gap: 18 },
                    { name: 'Disaster Recovery', gap: 15 },
                    { name: 'Access Control', gap: 12 },
                    { name: 'Incident Response', gap: 8 }
                  ]}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" domain={[0, 25]} />
                  <YAxis dataKey="name" type="category" />
                  <Tooltip />
                  <Bar dataKey="gap" fill="#FF8042" />
                </BarChart>
              </ResponsiveContainer>
              <Typography variant="body2" color="text.secondary">
                Areas with the highest percentage of unmet requirements.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DataVisualization;
