import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Paper, Grid, Button, TextField,
  Table, TableBody, TableCell, TableContainer, TableHead,
  TableRow, Chip, Card, CardContent, FormControl,
  InputLabel, Select, MenuItem, SelectChangeEvent,
  Switch, FormControlLabel, Dialog, DialogTitle, 
  DialogContent, DialogActions, IconButton, Divider,
  List, ListItem, ListItemText, ListItemIcon, ListItemSecondaryAction,
  Tabs, Tab
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  Schedule as ScheduleIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`notification-tabpanel-${index}`}
      aria-labelledby={`notification-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

interface Notification {
  id: number;
  type: 'email' | 'sms' | 'app';
  title: string;
  message: string;
  date: string;
  read: boolean;
  priority: 'high' | 'medium' | 'low';
}

interface NotificationRule {
  id: number;
  name: string;
  event: string;
  channels: string[];
  enabled: boolean;
}

interface ScheduledReport {
  id: number;
  name: string;
  reportType: string;
  frequency: string;
  nextDelivery: string;
  recipients: string[];
  enabled: boolean;
}

const NotificationSystem: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  
  // Notifications state
  const [notifications, setNotifications] = useState<Notification[]>([
    { id: 1, type: 'email', title: 'Policy Match Complete', message: 'Your policy matching job has completed with 15 matches found.', date: '2025-04-13 10:30 AM', read: false, priority: 'medium' },
    { id: 2, type: 'app', title: 'Compliance Alert', message: 'Security Policy has 3 unaddressed requirements.', date: '2025-04-12 02:15 PM', read: true, priority: 'high' },
    { id: 3, type: 'sms', title: 'Report Generated', message: 'Your scheduled compliance report has been generated.', date: '2025-04-11 09:45 AM', read: false, priority: 'low' },
    { id: 4, type: 'email', title: 'New Policy Added', message: 'A new policy "Data Protection Policy" has been added to the system.', date: '2025-04-10 11:20 AM', read: true, priority: 'medium' },
    { id: 5, type: 'app', title: 'System Update', message: 'ComplianceMax has been updated to version 2.5.', date: '2025-04-09 03:30 PM', read: true, priority: 'low' },
  ]);
  
  // Notification rules state
  const [notificationRules, setNotificationRules] = useState<NotificationRule[]>([
    { id: 1, name: 'Policy Match Completion', event: 'policy_match_complete', channels: ['email', 'app'], enabled: true },
    { id: 2, name: 'Compliance Alerts', event: 'compliance_alert', channels: ['email', 'sms', 'app'], enabled: true },
    { id: 3, name: 'Report Generation', event: 'report_generated', channels: ['email'], enabled: true },
    { id: 4, name: 'New Policy Added', event: 'policy_added', channels: ['app'], enabled: false },
    { id: 5, name: 'System Updates', event: 'system_update', channels: ['email', 'app'], enabled: true },
  ]);
  
  // Scheduled reports state
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([
    { id: 1, name: 'Weekly Compliance Summary', reportType: 'Compliance', frequency: 'Weekly', nextDelivery: '2025-04-20', recipients: ['<EMAIL>'], enabled: true },
    { id: 2, name: 'Monthly Policy Coverage', reportType: 'Policy', frequency: 'Monthly', nextDelivery: '2025-05-01', recipients: ['<EMAIL>', '<EMAIL>'], enabled: true },
    { id: 3, name: 'Quarterly Gap Analysis', reportType: 'Gap Analysis', frequency: 'Quarterly', nextDelivery: '2025-06-30', recipients: ['<EMAIL>'], enabled: true },
  ]);
  
  // Dialog states
  const [ruleDialogOpen, setRuleDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<NotificationRule | null>(null);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);
  const [editingReport, setEditingReport] = useState<ScheduledReport | null>(null);
  
  // Form states
  const [ruleName, setRuleName] = useState('');
  const [ruleEvent, setRuleEvent] = useState('');
  const [ruleChannels, setRuleChannels] = useState<string[]>([]);
  const [ruleEnabled, setRuleEnabled] = useState(true);
  
  const [reportName, setReportName] = useState('');
  const [reportType, setReportType] = useState('');
  const [reportFrequency, setReportFrequency] = useState('');
  const [reportRecipients, setReportRecipients] = useState('');
  const [reportEnabled, setReportEnabled] = useState(true);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleMarkAsRead = (id: number) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };
  
  const handleDeleteNotification = (id: number) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };
  
  const handleMarkAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
  };
  
  const handleToggleRule = (id: number) => {
    setNotificationRules(prev => 
      prev.map(rule => 
        rule.id === id ? { ...rule, enabled: !rule.enabled } : rule
      )
    );
  };
  
  const handleToggleReport = (id: number) => {
    setScheduledReports(prev => 
      prev.map(report => 
        report.id === id ? { ...report, enabled: !report.enabled } : report
      )
    );
  };
  
  const handleOpenRuleDialog = (rule?: NotificationRule) => {
    if (rule) {
      setEditingRule(rule);
      setRuleName(rule.name);
      setRuleEvent(rule.event);
      setRuleChannels(rule.channels);
      setRuleEnabled(rule.enabled);
    } else {
      setEditingRule(null);
      setRuleName('');
      setRuleEvent('');
      setRuleChannels([]);
      setRuleEnabled(true);
    }
    setRuleDialogOpen(true);
  };
  
  const handleCloseRuleDialog = () => {
    setRuleDialogOpen(false);
  };
  
  const handleOpenReportDialog = (report?: ScheduledReport) => {
    if (report) {
      setEditingReport(report);
      setReportName(report.name);
      setReportType(report.reportType);
      setReportFrequency(report.frequency);
      setReportRecipients(report.recipients.join(', '));
      setReportEnabled(report.enabled);
    } else {
      setEditingReport(null);
      setReportName('');
      setReportType('');
      setReportFrequency('');
      setReportRecipients('');
      setReportEnabled(true);
    }
    setReportDialogOpen(true);
  };
  
  const handleCloseReportDialog = () => {
    setReportDialogOpen(false);
  };
  
  const handleSaveRule = () => {
    if (!ruleName || !ruleEvent || ruleChannels.length === 0) {
      alert('Please fill in all required fields');
      return;
    }
    
    if (editingRule) {
      // Update existing rule
      setNotificationRules(prev => 
        prev.map(rule => 
          rule.id === editingRule.id 
            ? { ...rule, name: ruleName, event: ruleEvent, channels: ruleChannels, enabled: ruleEnabled }
            : rule
        )
      );
    } else {
      // Create new rule
      const newRule: NotificationRule = {
        id: Math.max(0, ...notificationRules.map(r => r.id)) + 1,
        name: ruleName,
        event: ruleEvent,
        channels: ruleChannels,
        enabled: ruleEnabled
      };
      setNotificationRules(prev => [...prev, newRule]);
    }
    
    handleCloseRuleDialog();
  };
  
  const handleSaveReport = () => {
    if (!reportName || !reportType || !reportFrequency || !reportRecipients) {
      alert('Please fill in all required fields');
      return;
    }
    
    const recipients = reportRecipients.split(',').map(email => email.trim());
    
    if (editingReport) {
      // Update existing report
      setScheduledReports(prev => 
        prev.map(report => 
          report.id === editingReport.id 
            ? { ...report, name: reportName, reportType, frequency: reportFrequency, recipients, enabled: reportEnabled }
            : report
        )
      );
    } else {
      // Create new report
      const newReport: ScheduledReport = {
        id: Math.max(0, ...scheduledReports.map(r => r.id)) + 1,
        name: reportName,
        reportType,
        frequency: reportFrequency,
        nextDelivery: calculateNextDelivery(reportFrequency),
        recipients,
        enabled: reportEnabled
      };
      setScheduledReports(prev => [...prev, newReport]);
    }
    
    handleCloseReportDialog();
  };
  
  const calculateNextDelivery = (frequency: string): string => {
    const now = new Date();
    let nextDate = new Date();
    
    switch (frequency) {
      case 'Daily':
        nextDate.setDate(now.getDate() + 1);
        break;
      case 'Weekly':
        nextDate.setDate(now.getDate() + 7);
        break;
      case 'Monthly':
        nextDate.setMonth(now.getMonth() + 1);
        break;
      case 'Quarterly':
        nextDate.setMonth(now.getMonth() + 3);
        break;
      default:
        nextDate.setDate(now.getDate() + 7);
    }
    
    return nextDate.toISOString().split('T')[0];
  };
  
  const handleChannelToggle = (channel: string) => {
    setRuleChannels(prev => 
      prev.includes(channel)
        ? prev.filter(c => c !== channel)
        : [...prev, channel]
    );
  };
  
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <EmailIcon />;
      case 'sms':
        return <SmsIcon />;
      case 'app':
        return <NotificationsIcon />;
      default:
        return <InfoIcon />;
    }
  };
  
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <WarningIcon color="error" />;
      case 'medium':
        return <InfoIcon color="warning" />;
      case 'low':
        return <CheckCircleIcon color="success" />;
      default:
        return <InfoIcon />;
    }
  };
  
  const unreadCount = notifications.filter(n => !n.read).length;
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Notification System
      </Typography>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label={`Notifications ${unreadCount > 0 ? `(${unreadCount})` : ''}`} />
          <Tab label="Notification Rules" />
          <Tab label="Scheduled Reports" />
        </Tabs>
      </Paper>
      
      {/* Notifications Tab */}
      <TabPanel value={tabValue} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
          <Button
            variant="outlined"
            onClick={handleMarkAllAsRead}
            disabled={unreadCount === 0}
          >
            Mark All as Read
          </Button>
        </Box>
        
        <List>
          {notifications.length > 0 ? (
            notifications.map((notification) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  alignItems="flex-start"
                  sx={{
                    bgcolor: notification.read ? 'transparent' : 'rgba(25, 118, 210, 0.08)',
                    borderRadius: 1
                  }}
                >
                  <ListItemIcon>
                    {getNotificationIcon(notification.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getPriorityIcon(notification.priority)}
                        <Typography sx={{ ml: 1, fontWeight: notification.read ? 'normal' : 'bold' }}>
                          {notification.title}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography
                          component="span"
                          variant="body2"
                          color="text.primary"
                        >
                          {notification.message}
                        </Typography>
                        <Typography
                          component="span"
                          variant="body2"
                          color="text.secondary"
                          sx={{ display: 'block', mt: 1 }}
                        >
                          {notification.date}
                        </Typography>
                      </>
                    }
                  />
                  <ListItemSecondaryAction>
                    {!notification.read && (
                      <IconButton edge="end" onClick={() => handleMarkAsRead(notification.id)}>
                        <CheckCircleIcon />
                      </IconButton>
                    )}
                    <IconButton edge="end" onClick={() => handleDeleteNotification(notification.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))
          ) : (
            <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
              No notifications to display
            </Typography>
          )}
        </List>
      </TabPanel>
      
      {/* Notification Rules Tab */}
      <TabPanel value={tabValue} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenRuleDialog()}
          >
            Add Rule
          </Button>
        </Box>
        
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Rule Name</TableCell>
                <TableCell>Event</TableCell>
                <TableCell>Channels</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {notificationRules.map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell>{rule.name}</TableCell>
                  <TableCell>{rule.event}</TableCell>
                  <TableCell>
                    {rule.channels.map((channel) => (
                      <Chip
                        key={channel}
                        icon={
                          channel === 'email' ? <EmailIcon /> :
                          channel === 'sms' ? <SmsIcon /> :
                          <NotificationsIcon />
                        }
                        label={channel.charAt(0).toUpperCase() + channel.slice(1)}
                        size="small"
                        sx={{ mr: 0.5 }}
                      />
                    ))}
                  </TableCell>
                  <TableCell>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={rule.enabled}
                          onChange={() => handleToggleRule(rule.id)}
                          color="primary"
                        />
                      }
                      label={rule.enabled ? "Enabled" : "Disabled"}
                    />
                  </TableCell>
                  <TableCell align="right">
                    <IconButton color="primary" onClick={() => handleOpenRuleDialog(rule)}>
                      <EditIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>
      
      {/* Scheduled Reports Tab */}
      <TabPanel value={tabValue} index={2}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenReportDialog()}
          >
            Schedule New Report
          </Button>
        </Box>
        
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Report Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Frequency</TableCell>
                <TableCell>Next Delivery</TableCell>
                <TableCell>Recipients</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {scheduledReports.map((report) => (
                <TableRow key={report.id}>
                  <TableCell>{report.name}</TableCell>
                  <TableCell>{report.reportType}</TableCell>
                  <TableCell>{report.frequency}</TableCell>
                  <TableCell>{report.nextDelivery}</TableCell>
                  <TableCell>
                    {report.recipients.length > 1 
                      ? `${report.recipients[0]} +${report.recipients.length - 1} more`
                      : report.recipients[0]}
                  </TableCell>
                  <TableCell>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={report.enabled}
                          onChange={() => handleToggleReport(report.id)}
                          color="primary"
                        />
                      }
                      label={report.enabled ? "Enabled" : "Disabled"}
                    />
                  </TableCell>
                  <TableCell align="right">
                    <IconButton color="primary" onClick={() => handleOpenReportDialog(report)}>
                      <EditIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>
      
      {/* Notification Rule Dialog */}
      <Dialog open={ruleDialogOpen} onClose={handleCloseRuleDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingRule ? 'Edit Notification Rule' : 'Create Notification Rule'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Rule Name"
            fullWidth
            variant="outlined"
            value={ruleName}
            onChange={(e) => setRuleName(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Event</InputLabel>
            <Select
              value={ruleEvent}
              label="Event"
              onChange={(e) => setRuleEvent(e.target.value)}
            >
              <MenuItem value="policy_match_complete">Policy Match Completion</MenuItem>
              <MenuItem value="compliance_alert">Compliance Alert</MenuItem>
              <MenuItem value="report_generated">Report Generated</MenuItem>
              <MenuItem value="policy_added">New Policy Added</MenuItem>
              <MenuItem value="system_update">System Update</MenuItem>
            </Select>
          </FormControl>
          
          <Typography variant="subtitle1" gutterBottom>
            Notification Channels:
          </Typography>
          
          <FormControlLabel
            control={
              <Checkbox
                checked={ruleChannels.includes('email')}
                onChange={() => handleChannelToggle('email')}
              />
            }
            label="Email"
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={ruleChannels.includes('sms')}
                onChange={() => handleChannelToggle('sms')}
              />
            }
            label="SMS"
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={ruleChannels.includes('app')}
                onChange={() => handleChannelToggle('app')}
              />
            }
            label="In-App"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={ruleEnabled}
                onChange={(e) => setRuleEnabled(e.target.checked)}
              />
            }
            label="Enable Rule"
            sx={{ mt: 2, display: 'block' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRuleDialog}>Cancel</Button>
          <Button onClick={handleSaveRule} variant="contained">
            {editingRule ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Scheduled Report Dialog */}
      <Dialog open={reportDialogOpen} onClose={handleCloseReportDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingReport ? 'Edit Scheduled Report' : 'Schedule New Report'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Report Name"
            fullWidth
            variant="outlined"
            value={reportName}
            onChange={(e) => setReportName(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Report Type</InputLabel>
            <Select
              value={reportType}
              label="Report Type"
              onChange={(e) => setReportType(e.target.value)}
            >
              <MenuItem value="Compliance">Compliance Report</MenuItem>
              <MenuItem value="Policy">Policy Coverage Report</MenuItem>
              <MenuItem value="Gap Analysis">Gap Analysis Report</MenuItem>
              <MenuItem value="Document">Document Completeness Report</MenuItem>
              <MenuItem value="Security">Security Assessment Report</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Frequency</InputLabel>
            <Select
              value={reportFrequency}
              label="Frequency"
              onChange={(e) => setReportFrequency(e.target.value)}
            >
              <MenuItem value="Daily">Daily</MenuItem>
              <MenuItem value="Weekly">Weekly</MenuItem>
              <MenuItem value="Monthly">Monthly</MenuItem>
              <MenuItem value="Quarterly">Quarterly</MenuItem>
            </Select>
          </FormControl>
          
          <TextField
            margin="dense"
            label="Recipients (comma separated emails)"
            fullWidth
            variant="outlined"
            value={reportRecipients}
            onChange={(e) => setReportRecipients(e.target.value)}
            sx={{ mb: 2 }}
            placeholder="<EMAIL>, <EMAIL>"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={reportEnabled}
                onChange={(e) => setReportEnabled(e.target.checked)}
              />
            }
            label="Enable Schedule"
            sx={{ mt: 2, display: 'block' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseReportDialog}>Cancel</Button>
          <Button onClick={handleSaveReport} variant="contained">
            {editingReport ? 'Update' : 'Schedule'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default NotificationSystem;
