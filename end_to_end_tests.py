import pytest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time
import os

# Configure headless Chrome for testing
@pytest.fixture
def driver():
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.set_window_size(1920, 1080)
    yield driver
    driver.quit()

# Test the complete policy matching workflow
def test_policy_matching_workflow(driver):
    """Test the complete policy matching workflow from upload to results"""
    # Navigate to the application
    driver.get("http://localhost:3000")
    
    # Login (assuming we have a login page)
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    driver.find_element(By.ID, "email").send_keys("<EMAIL>")
    driver.find_element(By.ID, "password").send_keys("password123")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Login')]").click()
    
    # Navigate to Policy Matcher
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Policy Matcher')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(), 'Policy Matcher')]").click()
    
    # Go to Upload Document tab
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Upload Document')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(), 'Upload Document')]").click()
    
    # Upload a document
    file_path = os.path.abspath("test_requirements.pdf")
    driver.find_element(By.XPATH, "//input[@type='file']").send_keys(file_path)
    
    # Select confidence threshold
    driver.find_element(By.ID, "confidence-threshold").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Medium (60%+)')]").click()
    
    # Click Match Policies button
    driver.find_element(By.XPATH, "//button[contains(text(), 'Match Policies')]").click()
    
    # Wait for processing to complete
    WebDriverWait(driver, 30).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Processing your document...')]"))
    )
    
    # Wait for results to appear (this might take some time in a real application)
    WebDriverWait(driver, 60).until(
        EC.presence_of_element_located((By.XPATH, "//th[contains(text(), 'Requirement')]"))
    )
    
    # Verify results are displayed
    assert driver.find_element(By.XPATH, "//th[contains(text(), 'Requirement')]").is_displayed()
    assert driver.find_element(By.XPATH, "//th[contains(text(), 'Policy')]").is_displayed()
    assert driver.find_element(By.XPATH, "//th[contains(text(), 'Confidence')]").is_displayed()
    
    # Download report
    driver.find_element(By.XPATH, "//button[contains(text(), 'Download Report')]").click()
    
    # Wait for download to complete (in a real test, would check for file existence)
    time.sleep(2)
    
    # Verify notification appears
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Report downloaded successfully')]"))
    )

# Test the data visualization component
def test_data_visualization(driver):
    """Test the data visualization component functionality"""
    # Navigate to the application and login
    driver.get("http://localhost:3000")
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    driver.find_element(By.ID, "email").send_keys("<EMAIL>")
    driver.find_element(By.ID, "password").send_keys("password123")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Login')]").click()
    
    # Navigate to Data Visualization
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Data Visualization')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(), 'Data Visualization')]").click()
    
    # Verify charts are displayed
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'recharts-wrapper')]"))
    )
    
    # Change chart type
    driver.find_element(By.ID, "chart-type").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Pie Chart')]").click()
    
    # Verify pie chart appears
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'recharts-pie')]"))
    )
    
    # Change data type
    driver.find_element(By.ID, "data-type").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Policy Matching Results')]").click()
    
    # Verify data changes
    time.sleep(2)  # Allow time for chart to update
    
    # Take screenshot for visual verification
    driver.save_screenshot("data_visualization_test.png")

# Test the export functionality
def test_export_functionality(driver):
    """Test the export functionality component"""
    # Navigate to the application and login
    driver.get("http://localhost:3000")
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    driver.find_element(By.ID, "email").send_keys("<EMAIL>")
    driver.find_element(By.ID, "password").send_keys("password123")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Login')]").click()
    
    # Navigate to Export Reports
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Export Reports')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(), 'Export Reports')]").click()
    
    # Select a report
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//td[contains(text(), 'Quarterly Compliance Report')]"))
    )
    driver.find_element(By.XPATH, "//td[contains(text(), 'Quarterly Compliance Report')]").click()
    
    # Select export format
    driver.find_element(By.ID, "export-format").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'PDF Document')]").click()
    
    # Select template
    driver.find_element(By.XPATH, "//button[contains(text(), 'Select Template')]").click()
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//li[contains(text(), 'Standard Report')]"))
    )
    driver.find_element(By.XPATH, "//li[contains(text(), 'Standard Report')]").click()
    
    # Click export button
    driver.find_element(By.XPATH, "//button[contains(text(), 'Export')]").click()
    
    # Verify export success message
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Export successful')]"))
    )

# Test the notification system
def test_notification_system(driver):
    """Test the notification system functionality"""
    # Navigate to the application and login
    driver.get("http://localhost:3000")
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    driver.find_element(By.ID, "email").send_keys("<EMAIL>")
    driver.find_element(By.ID, "password").send_keys("password123")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Login')]").click()
    
    # Navigate to Notification System
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Notification System')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(), 'Notification System')]").click()
    
    # Verify notifications are displayed
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Policy Match Complete')]"))
    )
    
    # Mark a notification as read
    unread_notification = driver.find_element(By.XPATH, "//div[contains(text(), 'Policy Match Complete')]")
    mark_as_read_button = unread_notification.find_element(By.XPATH, "./ancestor::li//button[1]")
    mark_as_read_button.click()
    
    # Go to Notification Rules tab
    driver.find_element(By.XPATH, "//span[contains(text(), 'Notification Rules')]").click()
    
    # Add a new rule
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Add Rule')]"))
    )
    driver.find_element(By.XPATH, "//button[contains(text(), 'Add Rule')]").click()
    
    # Fill in rule details
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[@label='Rule Name']"))
    )
    driver.find_element(By.XPATH, "//input[@label='Rule Name']").send_keys("Test Rule")
    
    # Select event type
    driver.find_element(By.ID, "event-select").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Policy Match Completion')]").click()
    
    # Select channels
    driver.find_element(By.XPATH, "//span[contains(text(), 'Email')]").click()
    
    # Save rule
    driver.find_element(By.XPATH, "//button[contains(text(), 'Create')]").click()
    
    # Verify rule was added
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//td[contains(text(), 'Test Rule')]"))
    )

# Test the enhanced dashboard
def test_enhanced_dashboard(driver):
    """Test the enhanced dashboard with trend analysis"""
    # Navigate to the application and login
    driver.get("http://localhost:3000")
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    driver.find_element(By.ID, "email").send_keys("<EMAIL>")
    driver.find_element(By.ID, "password").send_keys("password123")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Login')]").click()
    
    # Navigate to Enhanced Dashboard
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Enhanced Dashboard')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(), 'Enhanced Dashboard')]").click()
    
    # Verify trend charts are displayed
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'recharts-wrapper')]"))
    )
    
    # Change time range
    driver.find_element(By.ID, "time-range").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Last 3 Months')]").click()
    
    # Wait for data to update
    time.sleep(2)
    
    # Change to Category Analysis tab
    driver.find_element(By.XPATH, "//span[contains(text(), 'Category Analysis')]").click()
    
    # Verify category charts are displayed
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Compliance by Category Over Time')]"))
    )
    
    # Take screenshot for visual verification
    driver.save_screenshot("enhanced_dashboard_test.png")

# Test the external systems integration
def test_external_systems_integration(driver):
    """Test the external systems integration functionality"""
    # Navigate to the application and login
    driver.get("http://localhost:3000")
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    driver.find_element(By.ID, "email").send_keys("<EMAIL>")
    driver.find_element(By.ID, "password").send_keys("password123")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Login')]").click()
    
    # Navigate to External Systems Integration
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'External Systems')]"))
    )
    driver.find_element(By.XPATH, "//span[contains(text(), 'External Systems')]").click()
    
    # Test FEMA Database tab
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//h6[contains(text(), 'FEMA Disaster Database')]"))
    )
    
    # Search for a disaster
    search_input = driver.find_element(By.XPATH, "//input[@placeholder='Search disasters by ID, name, or state']")
    search_input.send_keys("Hurricane")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Search')]").click()
    
    # Verify search results
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//td[contains(text(), 'Hurricane')]"))
    )
    
    # Test Document Management tab
    driver.find_element(By.XPATH, "//span[contains(text(), 'Document Management')]").click()
    
    # Verify document repositories are displayed
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//h6[contains(text(), 'Connected Document Repositories')]"))
    )
    
    # Test Calendar Integration tab
    driver.find_element(By.XPATH, "//span[contains(text(), 'Calendar Integration')]").click()
    
    # Verify calendar events are displayed
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//h6[contains(text(), 'Compliance Calendar')]"))
    )
    
    # Add a new event
    driver.find_element(By.XPATH, "//button[contains(text(), 'Add Event')]").click()
    
    # Fill in event details
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[@label='Event Title']"))
    )
    driver.find_element(By.XPATH, "//input[@label='Event Title']").send_keys("Test Event")
    
    # Set dates
    start_date = driver.find_element(By.XPATH, "//input[@label='Start Date']")
    start_date.clear()
    start_date.send_keys("2025-05-01")
    
    end_date = driver.find_element(By.XPATH, "//input[@label='End Date']")
    end_date.clear()
    end_date.send_keys("2025-05-01")
    
    # Select event type
    driver.find_element(By.ID, "event-type").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Deadline')]").click()
    
    # Save event
    driver.find_element(By.XPATH, "//button[contains(text(), 'Add Event')]").click()
    
    # Verify event was added
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//td[contains(text(), 'Test Event')]"))
    )
